<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Sales Proposal Data Table</title>
    <link rel="stylesheet" href="style.css">
</head>
<body>
    <div class="container">
        <header>
            <h1>Sales Proposal Management</h1>
            <div class="controls">
                <input type="text" id="searchInput" placeholder="Search proposals...">
                <select id="statusFilter">
                    <option value="">All Status</option>
                    <option value="Initial">Initial</option>
                    <option value="In Progress">In Progress</option>
                    <option value="Approval Pending">Approval Pending</option>
                    <option value="Order Expected">Order Expected</option>
                    <option value="Closed">Closed</option>
                    <option value="Dropped">Dropped</option>
                </select>
            </div>
        </header>

        <div class="table-container">
            <table id="proposalTable">
                <thead>
                    <tr>
                        <th data-sort="accountName">Account Name <span class="sort-arrow"></span></th>
                        <th data-sort="product">Product <span class="sort-arrow"></span></th>
                        <th data-sort="vendor">Vendor <span class="sort-arrow"></span></th>
                        <th data-sort="value">Value (₹ Lakhs) <span class="sort-arrow"></span></th>
                        <th data-sort="submittedBy">Submitted By <span class="sort-arrow"></span></th>
                        <th data-sort="proposalDate">Proposal Date <span class="sort-arrow"></span></th>
                        <th data-sort="status">Status <span class="sort-arrow"></span></th>
                    </tr>
                </thead>
                <tbody id="tableBody">
                    <!-- Data will be populated by JavaScript -->
                </tbody>
            </table>
        </div>

        <div class="pagination">
            <button id="prevBtn" disabled>Previous</button>
            <span id="pageInfo">Page 1 of 1</span>
            <button id="nextBtn" disabled>Next</button>
            <select id="pageSize">
                <option value="10">10 per page</option>
                <option value="25" selected>25 per page</option>
                <option value="50">50 per page</option>
            </select>
        </div>

        <div class="summary">
            <div class="summary-item">
                <span class="label">Total Proposals:</span>
                <span id="totalCount">0</span>
            </div>
            <div class="summary-item">
                <span class="label">Total Value:</span>
                <span id="totalValue">₹0 Lakhs</span>
            </div>
        </div>
    </div>

    <script src="data.js"></script>
    <script src="script.js"></script>
</body>
</html>
