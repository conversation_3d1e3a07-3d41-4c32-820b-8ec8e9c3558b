// Initialize the DataTable when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    // Define table columns configuration
    const columns = [
        {
            key: 'accountName',
            title: 'Account Name',
            width: '18%'
        },
        {
            key: 'product',
            title: 'Product',
            width: '20%'
        },
        {
            key: 'vendor',
            title: 'Vendor',
            width: '12%'
        },
        {
            key: 'value',
            title: 'Value (₹ Lakhs)',
            type: 'currency',
            width: '12%'
        },
        {
            key: 'submittedBy',
            title: 'Submitted By',
            width: '12%'
        },
        {
            key: 'proposalDate',
            title: 'Proposal Date',
            type: 'date',
            width: '12%'
        },
        {
            key: 'status',
            title: 'Status',
            width: '14%',
            render: function(value) {
                const statusClass = value.toLowerCase().replace(/\s+/g, '-');
                return `<span class="status ${statusClass}">${value}</span>`;
            }
        }
    ];

    // Status options for filter
    const statusOptions = ['Initial', 'In Progress', 'Approval Pending', 'Order Expected', 'Closed', 'Dropped'];

    // Initialize DataTable
    const salesTable = new DataTable('salesProposalTable', {
        data: salesProposalData,
        columns: columns,
        title: 'Sales Proposal Management',
        searchable: true,
        sortable: true,
        pageable: true,
        showSummary: true,
        pageSize: 25,
        statusFilter: true,
        statusOptions: statusOptions
    });

    // Make table globally accessible if needed
    window.salesTable = salesTable;
});
