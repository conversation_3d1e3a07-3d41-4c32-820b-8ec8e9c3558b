// Load data from data.js
let currentData = [...salesProposalData];
let filteredData = [...salesProposalData];
let currentPage = 1;
let pageSize = 25;
let sortColumn = '';
let sortDirection = 'asc';

// DOM elements
const tableBody = document.getElementById('tableBody');
const searchInput = document.getElementById('searchInput');
const statusFilter = document.getElementById('statusFilter');
const prevBtn = document.getElementById('prevBtn');
const nextBtn = document.getElementById('nextBtn');
const pageInfo = document.getElementById('pageInfo');
const pageSizeSelect = document.getElementById('pageSize');
const totalCount = document.getElementById('totalCount');
const totalValue = document.getElementById('totalValue');

// Initialize the application
document.addEventListener('DOMContentLoaded', function() {
    setupEventListeners();
    renderTable();
    updateSummary();
});

// Event listeners
function setupEventListeners() {
    searchInput.addEventListener('input', handleSearch);
    statusFilter.addEventListener('change', handleStatusFilter);
    prevBtn.addEventListener('click', () => changePage(-1));
    nextBtn.addEventListener('click', () => changePage(1));
    pageSizeSelect.addEventListener('change', handlePageSizeChange);
    
    // Add sort listeners to table headers
    document.querySelectorAll('th[data-sort]').forEach(header => {
        header.addEventListener('click', () => handleSort(header.dataset.sort));
    });
}

// Search functionality
function handleSearch() {
    const searchTerm = searchInput.value.toLowerCase();
    filteredData = currentData.filter(item => 
        item.accountName.toLowerCase().includes(searchTerm) ||
        item.product.toLowerCase().includes(searchTerm) ||
        item.vendor.toLowerCase().includes(searchTerm) ||
        item.submittedBy.toLowerCase().includes(searchTerm) ||
        item.status.toLowerCase().includes(searchTerm)
    );
    currentPage = 1;
    renderTable();
    updateSummary();
}

// Status filter functionality
function handleStatusFilter() {
    const selectedStatus = statusFilter.value;
    if (selectedStatus === '') {
        filteredData = [...currentData];
    } else {
        filteredData = currentData.filter(item => item.status === selectedStatus);
    }
    
    // Apply search if there's a search term
    if (searchInput.value) {
        handleSearch();
        return;
    }
    
    currentPage = 1;
    renderTable();
    updateSummary();
}

// Sorting functionality
function handleSort(column) {
    if (sortColumn === column) {
        sortDirection = sortDirection === 'asc' ? 'desc' : 'asc';
    } else {
        sortColumn = column;
        sortDirection = 'asc';
    }
    
    filteredData.sort((a, b) => {
        let aVal = a[column];
        let bVal = b[column];
        
        // Handle different data types
        if (column === 'value') {
            aVal = parseFloat(aVal);
            bVal = parseFloat(bVal);
        } else if (column === 'proposalDate') {
            aVal = new Date(aVal);
            bVal = new Date(bVal);
        } else {
            aVal = aVal.toString().toLowerCase();
            bVal = bVal.toString().toLowerCase();
        }
        
        if (aVal < bVal) return sortDirection === 'asc' ? -1 : 1;
        if (aVal > bVal) return sortDirection === 'asc' ? 1 : -1;
        return 0;
    });
    
    updateSortIndicators();
    renderTable();
}

// Update sort indicators in table headers
function updateSortIndicators() {
    document.querySelectorAll('th[data-sort]').forEach(header => {
        header.classList.remove('sort-asc', 'sort-desc');
        if (header.dataset.sort === sortColumn) {
            header.classList.add(`sort-${sortDirection}`);
        }
    });
}

// Pagination
function changePage(direction) {
    const totalPages = Math.ceil(filteredData.length / pageSize);
    const newPage = currentPage + direction;
    
    if (newPage >= 1 && newPage <= totalPages) {
        currentPage = newPage;
        renderTable();
    }
}

function handlePageSizeChange() {
    pageSize = parseInt(pageSizeSelect.value);
    currentPage = 1;
    renderTable();
}

// Render table
function renderTable() {
    const startIndex = (currentPage - 1) * pageSize;
    const endIndex = startIndex + pageSize;
    const pageData = filteredData.slice(startIndex, endIndex);
    
    tableBody.innerHTML = '';
    
    pageData.forEach(item => {
        const row = document.createElement('tr');
        row.innerHTML = `
            <td>${item.accountName}</td>
            <td>${item.product}</td>
            <td>${item.vendor}</td>
            <td>₹${item.value.toFixed(1)}</td>
            <td>${item.submittedBy}</td>
            <td>${formatDate(item.proposalDate)}</td>
            <td><span class="status ${getStatusClass(item.status)}">${item.status}</span></td>
        `;
        tableBody.appendChild(row);
    });
    
    updatePaginationControls();
}

// Helper functions
function formatDate(dateString) {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-IN', {
        day: '2-digit',
        month: 'short',
        year: 'numeric'
    });
}

function getStatusClass(status) {
    return status.toLowerCase().replace(/\s+/g, '-');
}

function updatePaginationControls() {
    const totalPages = Math.ceil(filteredData.length / pageSize);
    
    prevBtn.disabled = currentPage === 1;
    nextBtn.disabled = currentPage === totalPages || totalPages === 0;
    
    pageInfo.textContent = `Page ${currentPage} of ${totalPages}`;
}

function updateSummary() {
    totalCount.textContent = filteredData.length;
    
    const total = filteredData.reduce((sum, item) => sum + item.value, 0);
    totalValue.textContent = `₹${total.toFixed(1)} Lakhs`;
}
