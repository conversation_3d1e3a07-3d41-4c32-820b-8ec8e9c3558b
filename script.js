// API Service Layer - Mimics backend integration
class SalesProposalAPI {
    constructor() {
        this.baseURL = '/api/v1'; // Future backend endpoint
        this.cache = new Map();
        this.requestDelay = 300; // Simulate network delay
    }

    // Simulate API call with loading states
    async makeRequest(endpoint, options = {}) {
        const cacheKey = `${endpoint}_${JSON.stringify(options)}`;

        // Check cache first (simulate backend caching)
        if (this.cache.has(cacheKey) && !options.forceRefresh) {
            await this.delay(50); // Minimal delay for cache hit
            return this.cache.get(cacheKey);
        }

        // Simulate network delay
        await this.delay(this.requestDelay);

        // Simulate potential network errors (5% chance)
        if (Math.random() < 0.05) {
            throw new Error('Network error: Unable to fetch data');
        }

        let result;
        switch (endpoint) {
            case '/proposals':
                result = await this.getProposals(options);
                break;
            case '/proposals/status-options':
                result = await this.getStatusOptions();
                break;
            case '/proposals/summary':
                result = await this.getSummary(options);
                break;
            default:
                throw new Error(`Unknown endpoint: ${endpoint}`);
        }

        // Cache the result
        this.cache.set(cacheKey, result);
        return result;
    }

    async getProposals(options = {}) {
        let data = [...salesProposalData]; // Simulate database query

        // Server-side filtering (simulate backend filtering)
        if (options.status) {
            data = data.filter(item => item.status === options.status);
        }

        if (options.search) {
            const searchTerm = options.search.toLowerCase();
            data = data.filter(item =>
                item.accountName.toLowerCase().includes(searchTerm) ||
                item.product.toLowerCase().includes(searchTerm) ||
                item.vendor.toLowerCase().includes(searchTerm) ||
                item.submittedBy.toLowerCase().includes(searchTerm) ||
                item.status.toLowerCase().includes(searchTerm)
            );
        }

        // Server-side sorting
        if (options.sortBy) {
            data.sort((a, b) => {
                let aVal = a[options.sortBy];
                let bVal = b[options.sortBy];

                if (options.sortBy === 'value') {
                    aVal = parseFloat(aVal) || 0;
                    bVal = parseFloat(bVal) || 0;
                } else if (options.sortBy === 'proposalDate') {
                    aVal = new Date(aVal);
                    bVal = new Date(bVal);
                } else {
                    aVal = aVal.toString().toLowerCase();
                    bVal = bVal.toString().toLowerCase();
                }

                const direction = options.sortDirection === 'desc' ? -1 : 1;
                if (aVal < bVal) return -1 * direction;
                if (aVal > bVal) return 1 * direction;
                return 0;
            });
        }

        // Server-side pagination
        const total = data.length;
        const page = options.page || 1;
        const limit = options.limit || 25;
        const offset = (page - 1) * limit;
        const paginatedData = data.slice(offset, offset + limit);

        return {
            data: paginatedData,
            pagination: {
                page,
                limit,
                total,
                totalPages: Math.ceil(total / limit)
            },
            timestamp: new Date().toISOString()
        };
    }

    async getStatusOptions() {
        return {
            data: ['Initial', 'In Progress', 'Approval Pending', 'Order Expected', 'Closed', 'Dropped'],
            timestamp: new Date().toISOString()
        };
    }

    async getSummary(options = {}) {
        const proposals = await this.getProposals({ ...options, page: 1, limit: 1000 }); // Get all for summary
        const totalValue = proposals.data.reduce((sum, item) => sum + (parseFloat(item.value) || 0), 0);

        return {
            data: {
                totalCount: proposals.pagination.total,
                totalValue: totalValue,
                averageValue: proposals.pagination.total > 0 ? totalValue / proposals.pagination.total : 0
            },
            timestamp: new Date().toISOString()
        };
    }

    delay(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }

    // Clear cache (useful for data updates)
    clearCache() {
        this.cache.clear();
    }
}

// Application Controller - Manages UI and API interactions
class SalesProposalController {
    constructor() {
        this.api = new SalesProposalAPI();
        this.table = null;
        this.currentFilters = {
            search: '',
            status: '',
            sortBy: '',
            sortDirection: 'asc',
            page: 1,
            limit: 25
        };
        this.isLoading = false;
    }

    async init() {
        try {
            this.showLoading();

            // Load initial data and configuration
            const [statusOptions, initialData] = await Promise.all([
                this.api.makeRequest('/proposals/status-options'),
                this.api.makeRequest('/proposals', this.currentFilters)
            ]);

            // Initialize DataTable with backend-like configuration
            this.table = new DataTable('salesProposalTable', {
                data: initialData.data,
                columns: this.getColumnConfig(),
                title: 'Sales Proposal Management',
                searchable: true,
                sortable: true,
                pageable: true,
                showSummary: true,
                pageSize: this.currentFilters.limit,
                statusFilter: true,
                statusOptions: statusOptions.data,
                // Backend integration callbacks
                onSearch: (searchTerm) => this.handleSearch(searchTerm),
                onFilter: (filterValue) => this.handleFilter(filterValue),
                onSort: (column, direction) => this.handleSort(column, direction),
                onPageChange: (page) => this.handlePageChange(page),
                onPageSizeChange: (size) => this.handlePageSizeChange(size)
            });

            // Update summary
            await this.updateSummary();

            this.hideLoading();
        } catch (error) {
            this.handleError(error);
        }
    }

    getColumnConfig() {
        return [
            {
                key: 'accountName',
                title: 'Account Name',
                width: '18%'
            },
            {
                key: 'product',
                title: 'Product',
                width: '20%'
            },
            {
                key: 'vendor',
                title: 'Vendor',
                width: '12%'
            },
            {
                key: 'value',
                title: 'Value (₹ Lakhs)',
                type: 'currency',
                width: '12%'
            },
            {
                key: 'submittedBy',
                title: 'Submitted By',
                width: '12%'
            },
            {
                key: 'proposalDate',
                title: 'Proposal Date',
                type: 'date',
                width: '12%'
            },
            {
                key: 'status',
                title: 'Status',
                width: '14%',
                render: function(value) {
                    const statusClass = value.toLowerCase().replace(/\s+/g, '-');
                    return `<span class="status ${statusClass}">${value}</span>`;
                }
            }
        ];
    }

    async handleSearch(searchTerm) {
        this.currentFilters.search = searchTerm;
        this.currentFilters.page = 1;
        await this.refreshData();
    }

    async handleFilter(filterValue) {
        this.currentFilters.status = filterValue;
        this.currentFilters.page = 1;
        await this.refreshData();
    }

    async handleSort(column, direction) {
        this.currentFilters.sortBy = column;
        this.currentFilters.sortDirection = direction;
        this.currentFilters.page = 1;
        await this.refreshData();
    }

    async handlePageChange(page) {
        this.currentFilters.page = page;
        await this.refreshData();
    }

    async handlePageSizeChange(size) {
        this.currentFilters.limit = size;
        this.currentFilters.page = 1;
        await this.refreshData();
    }

    async refreshData() {
        if (this.isLoading) return;

        try {
            this.showLoading();

            const response = await this.api.makeRequest('/proposals', this.currentFilters);
            this.table.updateData(response.data);

            await this.updateSummary();
            this.hideLoading();
        } catch (error) {
            this.handleError(error);
        }
    }

    async updateSummary() {
        try {
            const summary = await this.api.makeRequest('/proposals/summary', {
                search: this.currentFilters.search,
                status: this.currentFilters.status
            });

            // Update summary in the table
            if (this.table && this.table.updateSummary) {
                this.table.updateSummaryData(summary.data);
            }
        } catch (error) {
            console.warn('Failed to update summary:', error);
        }
    }

    showLoading() {
        this.isLoading = true;
        // Add loading indicator to UI
        const container = document.getElementById('salesProposalTable');
        if (container && !container.querySelector('.loading-overlay')) {
            const loadingDiv = document.createElement('div');
            loadingDiv.className = 'loading-overlay';
            loadingDiv.innerHTML = '<div class="loading-spinner">Loading...</div>';
            container.appendChild(loadingDiv);
        }
    }

    hideLoading() {
        this.isLoading = false;
        // Remove loading indicator
        const loadingOverlay = document.querySelector('.loading-overlay');
        if (loadingOverlay) {
            loadingOverlay.remove();
        }
    }

    handleError(error) {
        this.hideLoading();
        console.error('API Error:', error);

        // Show user-friendly error message
        const container = document.getElementById('salesProposalTable');
        if (container) {
            const errorDiv = document.createElement('div');
            errorDiv.className = 'error-message';
            errorDiv.innerHTML = `
                <div class="error-content">
                    <h3>Error Loading Data</h3>
                    <p>${error.message}</p>
                    <button onclick="location.reload()">Retry</button>
                </div>
            `;
            container.innerHTML = '';
            container.appendChild(errorDiv);
        }
    }

    // Public methods for external use
    async reload() {
        this.api.clearCache();
        await this.refreshData();
    }

    getCurrentFilters() {
        return { ...this.currentFilters };
    }
}

// Initialize the application
document.addEventListener('DOMContentLoaded', async function() {
    const controller = new SalesProposalController();
    await controller.init();

    // Make controller globally accessible for debugging/external use
    window.salesController = controller;
});
