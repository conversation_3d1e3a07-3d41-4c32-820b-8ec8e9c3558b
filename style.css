* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background-color: #f5f5f5;
    color: #333;
    line-height: 1.6;
    height: 100vh;
    overflow: hidden;
}

.container {
    max-width: 1400px;
    margin: 0 auto;
    padding: 20px;
    height: 100vh;
    display: flex;
    flex-direction: column;
    gap: 20px;
}

header {
    background: white;
    padding: 20px;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    flex-shrink: 0;
}

header h1 {
    color: #2c3e50;
    margin-bottom: 15px;
    font-size: 2rem;
}

.controls {
    display: flex;
    gap: 15px;
    flex-wrap: wrap;
}

#searchInput {
    flex: 1;
    min-width: 250px;
    padding: 10px;
    border: 2px solid #ddd;
    border-radius: 5px;
    font-size: 14px;
}

#searchInput:focus {
    outline: none;
    border-color: #3498db;
}

#statusFilter {
    padding: 10px;
    border: 2px solid #ddd;
    border-radius: 5px;
    font-size: 14px;
    background: white;
}

.table-container {
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    overflow: hidden;
    flex: 1;
    display: flex;
    flex-direction: column;
    min-height: 0;
}

table {
    width: 100%;
    border-collapse: collapse;
    display: flex;
    flex-direction: column;
    height: 100%;
}

thead {
    flex-shrink: 0;
}

tbody {
    flex: 1;
    overflow-y: auto;
    display: block;
}

thead tr,
tbody tr {
    display: table;
    width: 100%;
    table-layout: fixed;
}

th {
    background: #34495e;
    color: white;
    padding: 15px 10px;
    text-align: left;
    font-weight: 600;
    cursor: pointer;
    user-select: none;
    position: relative;
}

th:hover {
    background: #2c3e50;
}

.sort-arrow {
    position: absolute;
    right: 8px;
    top: 50%;
    transform: translateY(-50%);
    opacity: 0.5;
}

.sort-arrow::after {
    content: '↕';
}

th.sort-asc .sort-arrow::after {
    content: '↑';
    opacity: 1;
}

th.sort-desc .sort-arrow::after {
    content: '↓';
    opacity: 1;
}

td {
    padding: 12px 10px;
    border-bottom: 1px solid #eee;
}

tr:hover {
    background-color: #f8f9fa;
}

.status {
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 600;
    text-align: center;
    display: inline-block;
    min-width: 80px;
}

.status.initial {
    background: #e3f2fd;
    color: #1976d2;
}

.status.in-progress {
    background: #fff3e0;
    color: #f57c00;
}

.status.approval-pending {
    background: #fce4ec;
    color: #c2185b;
}

.status.order-expected {
    background: #f3e5f5;
    color: #7b1fa2;
}

.status.closed {
    background: #e8f5e8;
    color: #388e3c;
}

.status.dropped {
    background: #ffebee;
    color: #d32f2f;
}

.pagination {
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: white;
    padding: 15px 20px;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    flex-shrink: 0;
}

.pagination button {
    padding: 8px 16px;
    border: 1px solid #ddd;
    background: white;
    border-radius: 4px;
    cursor: pointer;
    font-size: 14px;
}

.pagination button:hover:not(:disabled) {
    background: #f8f9fa;
}

.pagination button:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

#pageSize {
    padding: 8px;
    border: 1px solid #ddd;
    border-radius: 4px;
    background: white;
}

.summary {
    display: flex;
    gap: 20px;
    background: white;
    padding: 20px;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    flex-shrink: 0;
}

.summary-item {
    display: flex;
    flex-direction: column;
    align-items: center;
}

.summary-item .label {
    font-size: 14px;
    color: #666;
    margin-bottom: 5px;
}

.summary-item span:last-child {
    font-size: 18px;
    font-weight: 600;
    color: #2c3e50;
}

@media (max-width: 768px) {
    .container {
        padding: 10px;
    }
    
    .controls {
        flex-direction: column;
    }
    
    #searchInput {
        min-width: auto;
    }
    
    .table-container {
        overflow-x: auto;
    }
    
    table {
        min-width: 800px;
    }
    
    .pagination {
        flex-wrap: wrap;
        gap: 10px;
    }
    
    .summary {
        flex-direction: column;
        gap: 10px;
    }
}
