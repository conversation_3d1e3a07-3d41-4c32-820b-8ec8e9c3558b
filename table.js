class DataTable {
    constructor(containerId, options = {}) {
        this.containerId = containerId;
        this.container = document.getElementById(containerId);
        this.data = options.data || [];
        this.columns = options.columns || [];
        this.title = options.title || 'Data Table';
        this.searchable = options.searchable !== false;
        this.sortable = options.sortable !== false;
        this.pageable = options.pageable !== false;
        this.showSummary = options.showSummary !== false;
        this.pageSize = options.pageSize || 25;
        this.statusFilter = options.statusFilter || false;
        this.statusOptions = options.statusOptions || [];
        
        // Internal state
        this.currentData = [...this.data];
        this.filteredData = [...this.data];
        this.currentPage = 1;
        this.sortColumn = '';
        this.sortDirection = 'asc';
        
        this.init();
    }
    
    init() {
        this.createTableStructure();
        this.setupEventListeners();
        this.renderTable();
        this.updateSummary();
    }
    
    createTableStructure() {
        const html = `
            <div class="container">
                <header>
                    <h1>${this.title}</h1>
                    <div class="controls">
                        ${this.searchable ? '<input type="text" id="searchInput" placeholder="Search...">' : ''}
                        ${this.statusFilter ? this.createStatusFilter() : ''}
                    </div>
                </header>

                <div class="table-container">
                    <table id="dataTable">
                        <thead>
                            <tr>
                                ${this.columns.map(col => 
                                    `<th data-sort="${col.key}" ${col.width ? `style="width: ${col.width}"` : ''}>
                                        ${col.title} 
                                        ${this.sortable ? '<span class="sort-arrow"></span>' : ''}
                                    </th>`
                                ).join('')}
                            </tr>
                        </thead>
                        <tbody id="tableBody">
                            <!-- Data will be populated by JavaScript -->
                        </tbody>
                    </table>
                </div>

                ${this.pageable ? this.createPaginationHTML() : ''}
                ${this.showSummary ? this.createSummaryHTML() : ''}
            </div>
        `;
        
        this.container.innerHTML = html;
        this.cacheElements();
    }
    
    createStatusFilter() {
        return `
            <select id="statusFilter">
                <option value="">All Status</option>
                ${this.statusOptions.map(status => `<option value="${status}">${status}</option>`).join('')}
            </select>
        `;
    }
    
    createPaginationHTML() {
        return `
            <div class="pagination">
                <button id="prevBtn" disabled>Previous</button>
                <span id="pageInfo">Page 1 of 1</span>
                <button id="nextBtn" disabled>Next</button>
                <select id="pageSize">
                    <option value="10">10 per page</option>
                    <option value="25" ${this.pageSize === 25 ? 'selected' : ''}>25 per page</option>
                    <option value="50">50 per page</option>
                </select>
            </div>
        `;
    }
    
    createSummaryHTML() {
        return `
            <div class="summary">
                <div class="summary-item">
                    <span class="label">Total Records:</span>
                    <span id="totalCount">0</span>
                </div>
                <div class="summary-item">
                    <span class="label">Total Value:</span>
                    <span id="totalValue">₹0</span>
                </div>
            </div>
        `;
    }
    
    cacheElements() {
        this.tableBody = this.container.querySelector('#tableBody');
        this.searchInput = this.container.querySelector('#searchInput');
        this.statusFilterEl = this.container.querySelector('#statusFilter');
        this.prevBtn = this.container.querySelector('#prevBtn');
        this.nextBtn = this.container.querySelector('#nextBtn');
        this.pageInfo = this.container.querySelector('#pageInfo');
        this.pageSizeSelect = this.container.querySelector('#pageSize');
        this.totalCount = this.container.querySelector('#totalCount');
        this.totalValue = this.container.querySelector('#totalValue');
    }
    
    setupEventListeners() {
        if (this.searchInput) {
            this.searchInput.addEventListener('input', () => this.handleSearch());
        }
        
        if (this.statusFilterEl) {
            this.statusFilterEl.addEventListener('change', () => this.handleStatusFilter());
        }
        
        if (this.prevBtn) {
            this.prevBtn.addEventListener('click', () => this.changePage(-1));
        }
        
        if (this.nextBtn) {
            this.nextBtn.addEventListener('click', () => this.changePage(1));
        }
        
        if (this.pageSizeSelect) {
            this.pageSizeSelect.addEventListener('change', () => this.handlePageSizeChange());
        }
        
        if (this.sortable) {
            this.container.querySelectorAll('th[data-sort]').forEach(header => {
                header.addEventListener('click', () => this.handleSort(header.dataset.sort));
            });
        }
    }
    
    handleSearch() {
        const searchTerm = this.searchInput.value.toLowerCase();
        this.filteredData = this.currentData.filter(item => 
            this.columns.some(col => {
                const value = this.getCellValue(item, col);
                return value.toString().toLowerCase().includes(searchTerm);
            })
        );
        this.currentPage = 1;
        this.renderTable();
        this.updateSummary();
    }
    
    handleStatusFilter() {
        const selectedStatus = this.statusFilterEl.value;
        if (selectedStatus === '') {
            this.filteredData = [...this.currentData];
        } else {
            this.filteredData = this.currentData.filter(item => item.status === selectedStatus);
        }
        
        if (this.searchInput && this.searchInput.value) {
            this.handleSearch();
            return;
        }
        
        this.currentPage = 1;
        this.renderTable();
        this.updateSummary();
    }
    
    handleSort(column) {
        if (this.sortColumn === column) {
            this.sortDirection = this.sortDirection === 'asc' ? 'desc' : 'asc';
        } else {
            this.sortColumn = column;
            this.sortDirection = 'asc';
        }
        
        this.filteredData.sort((a, b) => {
            let aVal = a[column];
            let bVal = b[column];
            
            // Handle different data types
            const columnConfig = this.columns.find(col => col.key === column);
            if (columnConfig && columnConfig.type === 'number') {
                aVal = parseFloat(aVal) || 0;
                bVal = parseFloat(bVal) || 0;
            } else if (columnConfig && columnConfig.type === 'date') {
                aVal = new Date(aVal);
                bVal = new Date(bVal);
            } else {
                aVal = aVal.toString().toLowerCase();
                bVal = bVal.toString().toLowerCase();
            }
            
            if (aVal < bVal) return this.sortDirection === 'asc' ? -1 : 1;
            if (aVal > bVal) return this.sortDirection === 'asc' ? 1 : -1;
            return 0;
        });
        
        this.updateSortIndicators();
        this.renderTable();
    }
    
    updateSortIndicators() {
        this.container.querySelectorAll('th[data-sort]').forEach(header => {
            header.classList.remove('sort-asc', 'sort-desc');
            if (header.dataset.sort === this.sortColumn) {
                header.classList.add(`sort-${this.sortDirection}`);
            }
        });
    }
    
    changePage(direction) {
        const totalPages = Math.ceil(this.filteredData.length / this.pageSize);
        const newPage = this.currentPage + direction;
        
        if (newPage >= 1 && newPage <= totalPages) {
            this.currentPage = newPage;
            this.renderTable();
        }
    }
    
    handlePageSizeChange() {
        this.pageSize = parseInt(this.pageSizeSelect.value);
        this.currentPage = 1;
        this.renderTable();
    }
    
    renderTable() {
        if (!this.tableBody) return;
        
        const startIndex = (this.currentPage - 1) * this.pageSize;
        const endIndex = startIndex + this.pageSize;
        const pageData = this.filteredData.slice(startIndex, endIndex);
        
        this.tableBody.innerHTML = '';
        
        pageData.forEach(item => {
            const row = document.createElement('tr');
            row.innerHTML = this.columns.map(col => 
                `<td>${this.getCellValue(item, col)}</td>`
            ).join('');
            this.tableBody.appendChild(row);
        });
        
        this.updatePaginationControls();
    }
    
    getCellValue(item, column) {
        let value = item[column.key];
        
        if (column.render && typeof column.render === 'function') {
            return column.render(value, item);
        }
        
        if (column.type === 'currency') {
            return `₹${parseFloat(value).toFixed(1)}`;
        }
        
        if (column.type === 'date') {
            const date = new Date(value);
            return date.toLocaleDateString('en-IN', {
                day: '2-digit',
                month: 'short',
                year: 'numeric'
            });
        }
        
        return value;
    }
    
    updatePaginationControls() {
        if (!this.pageable) return;
        
        const totalPages = Math.ceil(this.filteredData.length / this.pageSize);
        
        if (this.prevBtn) this.prevBtn.disabled = this.currentPage === 1;
        if (this.nextBtn) this.nextBtn.disabled = this.currentPage === totalPages || totalPages === 0;
        if (this.pageInfo) this.pageInfo.textContent = `Page ${this.currentPage} of ${totalPages}`;
    }
    
    updateSummary() {
        if (!this.showSummary) return;
        
        if (this.totalCount) {
            this.totalCount.textContent = this.filteredData.length;
        }
        
        if (this.totalValue) {
            const valueColumn = this.columns.find(col => col.key === 'value');
            if (valueColumn) {
                const total = this.filteredData.reduce((sum, item) => sum + (parseFloat(item.value) || 0), 0);
                this.totalValue.textContent = `₹${total.toFixed(1)} Lakhs`;
            }
        }
    }
    
    // Public methods
    updateData(newData) {
        this.data = newData;
        this.currentData = [...newData];
        this.filteredData = [...newData];
        this.currentPage = 1;
        this.renderTable();
        this.updateSummary();
    }
    
    refresh() {
        this.renderTable();
        this.updateSummary();
    }
}
